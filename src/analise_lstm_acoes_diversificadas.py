#!/usr/bin/env python3
"""
Script para análise das 20 ações diversificadas usando LSTM
Substitui o filtro de <PERSON>lman por LSTM para previsão de preços
Usa dados de Open, Close, Low, High, Volume e Spread como features
Mantém a média móvel de 200 dias e faz previsão 20 dias à frente
"""

import yfinance as yf
import matplotlib.pyplot as plt
import pandas as pd
import numpy as np
import warnings
import os
import sys
from datetime import datetime, timedelta
import tensorflow as tf
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import LSTM, Dense, Dropout
from sklearn.preprocessing import MinMaxScaler
from sklearn.metrics import mean_squared_error, mean_absolute_error
import math

# Adicionar o diretório src ao path para importar functions
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from functions import edge_rolling

warnings.filterwarnings('ignore')
tf.get_logger().setLevel('ERROR')

# Configurar matplotlib
import matplotlib
matplotlib.use('Agg')  # Backend para salvar sem display

def carregar_acoes_diversificadas():
    """
    Carrega as 20 ações do arquivo CSV de diversificação
    """
    try:
        csv_path = 'results/csv/correlation_data/acoes_diversificacao.csv'
        df = pd.read_csv(csv_path)
        
        # Pegar apenas as primeiras 20 ações (excluindo linha vazia no final)
        acoes = []
        for _, row in df.head(20).iterrows():
            ticker = row['Ticker'] + '.SA'
            nome = row['Nome']
            acoes.append((ticker, nome))
        
        print(f"📋 Carregadas {len(acoes)} ações diversificadas do arquivo CSV")
        return acoes
        
    except Exception as e:
        print(f"❌ Erro ao carregar arquivo CSV: {e}")
        return []

def preparar_dados_lstm(dados, features_cols, target_col='Close', lookback=60, test_size=0.2):
    """
    Prepara os dados para treinamento do LSTM
    
    Args:
        dados: DataFrame com dados históricos
        features_cols: Lista de colunas para usar como features
        target_col: Coluna alvo para previsão
        lookback: Número de dias anteriores para usar como entrada
        test_size: Proporção dos dados para teste
    
    Returns:
        X_train, X_test, y_train, y_test, scaler_features, scaler_target
    """
    # Remover NaN e garantir dados suficientes
    dados_clean = dados[features_cols + [target_col]].dropna()
    
    if len(dados_clean) < lookback + 50:
        raise ValueError(f"Dados insuficientes: {len(dados_clean)} registros")
    
    # Normalizar features e target separadamente
    scaler_features = MinMaxScaler(feature_range=(0, 1))
    scaler_target = MinMaxScaler(feature_range=(0, 1))
    
    features_scaled = scaler_features.fit_transform(dados_clean[features_cols])
    target_scaled = scaler_target.fit_transform(dados_clean[[target_col]])
    
    # Criar sequências para LSTM
    X, y = [], []
    for i in range(lookback, len(features_scaled)):
        X.append(features_scaled[i-lookback:i])  # lookback dias de features
        y.append(target_scaled[i, 0])  # preço do dia atual
    
    X, y = np.array(X), np.array(y)
    
    # Dividir em treino e teste
    split_idx = int(len(X) * (1 - test_size))
    X_train, X_test = X[:split_idx], X[split_idx:]
    y_train, y_test = y[:split_idx], y[split_idx:]
    
    return X_train, X_test, y_train, y_test, scaler_features, scaler_target, dados_clean

def criar_modelo_lstm(input_shape, lstm_units=[50, 50], dropout_rate=0.2):
    """
    Cria modelo LSTM para previsão de preços
    
    Args:
        input_shape: Formato da entrada (timesteps, features)
        lstm_units: Lista com número de unidades para cada camada LSTM
        dropout_rate: Taxa de dropout para regularização
    
    Returns:
        Modelo LSTM compilado
    """
    model = Sequential()
    
    # Primeira camada LSTM
    model.add(LSTM(lstm_units[0], 
                   return_sequences=len(lstm_units) > 1,
                   input_shape=input_shape))
    model.add(Dropout(dropout_rate))
    
    # Camadas LSTM adicionais
    for i, units in enumerate(lstm_units[1:], 1):
        return_seq = i < len(lstm_units) - 1
        model.add(LSTM(units, return_sequences=return_seq))
        model.add(Dropout(dropout_rate))
    
    # Camada de saída
    model.add(Dense(1))
    
    # Compilar modelo
    model.compile(optimizer='adam', 
                  loss='mse',
                  metrics=['mae'])
    
    return model

def treinar_lstm(X_train, y_train, X_test, y_test, input_shape, 
                 epochs=50, batch_size=32, verbose=0):
    """
    Treina o modelo LSTM
    
    Returns:
        model, history, train_score, test_score
    """
    # Criar modelo
    model = criar_modelo_lstm(input_shape)
    
    # Treinar modelo
    history = model.fit(X_train, y_train,
                       epochs=epochs,
                       batch_size=batch_size,
                       validation_data=(X_test, y_test),
                       verbose=verbose,
                       shuffle=False)  # Importante para séries temporais
    
    # Avaliar modelo
    train_pred = model.predict(X_train, verbose=0)
    test_pred = model.predict(X_test, verbose=0)
    
    train_score = math.sqrt(mean_squared_error(y_train, train_pred))
    test_score = math.sqrt(mean_squared_error(y_test, test_pred))
    
    return model, history, train_score, test_score

def prever_lstm(model, dados_recentes, scaler_features, scaler_target, 
                features_cols, lookback=60, dias_previsao=20):
    """
    Faz previsões futuras usando o modelo LSTM treinado
    
    Args:
        model: Modelo LSTM treinado
        dados_recentes: Últimos dados para usar como base
        scaler_features: Scaler das features
        scaler_target: Scaler do target
        features_cols: Colunas de features
        lookback: Janela de lookback
        dias_previsao: Número de dias para prever
    
    Returns:
        Array com previsões
    """
    # Preparar dados recentes
    dados_scaled = scaler_features.transform(dados_recentes[features_cols].iloc[-lookback:])
    
    previsoes = []
    entrada_atual = dados_scaled.copy()
    
    for _ in range(dias_previsao):
        # Fazer previsão
        entrada_lstm = entrada_atual.reshape(1, lookback, len(features_cols))
        pred_scaled = model.predict(entrada_lstm, verbose=0)[0, 0]
        
        # Converter previsão de volta para escala original
        pred_original = scaler_target.inverse_transform([[pred_scaled]])[0, 0]
        previsoes.append(pred_original)
        
        # Atualizar entrada para próxima previsão
        # Usar a previsão como novo Close e manter outras features constantes
        nova_linha = entrada_atual[-1].copy()
        nova_linha[features_cols.index('Close')] = pred_scaled
        
        # Deslizar janela
        entrada_atual = np.vstack([entrada_atual[1:], nova_linha.reshape(1, -1)])
    
    return np.array(previsoes)
